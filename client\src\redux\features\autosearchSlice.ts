import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { searchProperties } from "../actions/autoSearchAction";
import { Property } from "../../pages/Api/usage-pages/autosearch/models";

export interface AutosearchState {
  results: Property[];
  loading: boolean;
  error: string | null;
  lastSearch: string | null;
}

const initialState: AutosearchState = {
  results: [],
  loading: false,
  error: null,
  lastSearch: null,
};

const autosearchSlice = createSlice({
  name: "autosearch",
  initialState,
  reducers: {
    clearSearchResults: (state) => {
      state.results = [];
      state.error = null;
      state.lastSearch = null;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchProperties.pending, (state, action) => {
        state.loading = true;
        state.error = null;
        // Store the search string directly
        state.lastSearch = action.meta.arg.formData.search;
      })
      .addCase(searchProperties.fulfilled, (state, action) => {
        state.loading = false;
        state.results =
          action.payload.output.data?.Output ||
          action.payload.output.data ||
          [];
        state.error = null;
      })
      .addCase(searchProperties.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as string) || "An error occurred during search";
        state.results = [];
      });
  },
});

export const { clearSearchResults, setError } = autosearchSlice.actions;

// Selectors
export const selectAutosearchResults = (state: RootState) =>
  state.autosearch.results;
export const selectAutosearchLoading = (state: RootState) =>
  state.autosearch.loading;
export const selectAutosearchError = (state: RootState) =>
  state.autosearch.error;
export const selectLastSearch = (state: RootState) =>
  state.autosearch.lastSearch;

export default autosearchSlice.reducer;
