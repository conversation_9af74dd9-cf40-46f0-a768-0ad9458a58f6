import React, { useState, useEffect } from "react";
import {
  FaHome,
  FaMapMarkerAlt,
  FaEuroSign,
  FaCalendarAlt,
  FaBuilding,
  FaRedo,
  FaPlus,
} from "react-icons/fa";
import { useHistory } from "react-router-dom";
import { Property } from "../models";
import { fetchAddressData } from "../../../../../helpers/addressUtils";
import { useAppDispatch } from "../../../../../redux/hooks";
import { clearSearchResults } from "../../../../../redux/features/autosearchSlice";

interface SearchResultsProps {
  results: Property[];
  loading: boolean;
  error: string | null;
}

interface PropertyImage {
  [key: string]: {
    imageUrl: string;
    loading: boolean;
    error: boolean;
  };
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  loading,
  error,
}) => {
  const [propertyImages, setPropertyImages] = useState<PropertyImage>({});
  const history = useHistory();
  const dispatch = useAppDispatch();

  // Clear results and redirect to form
  const clearResults = () => {
    dispatch(clearSearchResults());
    history.push("/autosearch");
  };

  // Modify results (same as clear in this context)
  const modifyResults = () => {
    dispatch(clearSearchResults());
    history.push("/autosearch");
  };

  // Fetch images for properties that don't have them
  useEffect(() => {
    if (results && results.length > 0) {
      results.forEach(async (property) => {
        const propertyKey =
          property.bagid || `${property.postcode}-${property.housenumber}`;

        // Skip if already loading, loaded, or has an existing image
        if (propertyImages[propertyKey] || property.image) {
          return;
        }

        // Set loading state
        setPropertyImages((prev) => ({
          ...prev,
          [propertyKey]: { imageUrl: "", loading: true, error: false },
        }));

        try {
          // Create postal address string in the format expected by fetchAddressData
          const postalAddress = `${property.postcode}-${property.housenumber}-${
            property.houseaddition || ""
          }`;
          const addressData = await fetchAddressData(postalAddress);

          if (addressData) {
            setPropertyImages((prev) => ({
              ...prev,
              [propertyKey]: {
                imageUrl: addressData.imageUrl,
                loading: false,
                error: false,
              },
            }));
          } else {
            setPropertyImages((prev) => ({
              ...prev,
              [propertyKey]: { imageUrl: "", loading: false, error: true },
            }));
          }
        } catch (err) {
          setPropertyImages((prev) => ({
            ...prev,
            [propertyKey]: { imageUrl: "", loading: false, error: true },
          }));
        }
      });
    }
  }, [results, propertyImages]);

  if (loading) {
    return (
      <div
        className="bg-white rounded-lg p-6 mt-6"
        style={{
          boxShadow: "0px 5px 8px 0px rgba(15, 9, 83, 0.09)",
          border: "1px solid rgba(48, 57, 105, 0.15)",
        }}
      >
        <div className="animate-pulse space-y-6">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex gap-4">
              <div className="w-28 h-28 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-3">
                <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-white rounded-lg p-6 mt-6"
        style={{
          boxShadow: "0px 5px 8px 0px rgba(15, 9, 83, 0.09)",
          border: "1px solid rgba(48, 57, 105, 0.15)",
        }}
      >
        <div className="text-center">
          <div className="font-bold mb-2" style={{ color: "#1E9D66" }}>
            Er is een fout opgetreden
          </div>
          <div style={{ color: "#818589" }}>{error}</div>
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div
        className="bg-white rounded-lg p-6 mt-6"
        style={{
          boxShadow: "0px 5px 8px 0px rgba(15, 9, 83, 0.09)",
          border: "1px solid rgba(48, 57, 105, 0.15)",
        }}
      >
        <div className="text-center" style={{ color: "#818589" }}>
          <FaHome
            className="mx-auto mb-3 text-3xl"
            style={{ color: "#818589", opacity: 0.5 }}
          />
          <div className="font-bold mb-1">Geen resultaten gevonden</div>
          <div className="text-sm">Probeer een andere zoekopdracht</div>
        </div>
      </div>
    );
  }

  const getPropertyImage = (property: Property) => {
    const propertyKey =
      property.bagid || `${property.postcode}-${property.housenumber}`;

    // Return existing image if available
    if (property.image) {
      return { url: property.image, loading: false, error: false };
    }

    // Return fetched image data
    const imageData = propertyImages[propertyKey];
    if (imageData) {
      return {
        url: imageData.imageUrl,
        loading: imageData.loading,
        error: imageData.error,
      };
    }

    // Default state
    return { url: "", loading: true, error: false };
  };

  return (
    <div
      className="bg-white rounded-lg p-6 mt-6"
      style={{
        boxShadow: "0px 5px 8px 0px rgba(15, 9, 83, 0.09)",
        border: "1px solid rgba(48, 57, 105, 0.15)",
        maxHeight: "600px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Fixed Header */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <h3
          className="text-xl font-bold"
          style={{ color: "black", opacity: 0.8 }}
        >
          {results.length} {results.length === 1 ? "resultaat" : "resultaten"}{" "}
          gevonden
        </h3>
        <div className="text-sm" style={{ color: "#818589" }}>
          Gesorteerd op relevantie
        </div>
      </div>

      {/* Scrollable Results Container */}
      <div
        className="flex-1 overflow-y-auto pr-2"
        style={{
          maxHeight: "450px",
          scrollbarWidth: "thin",
          scrollbarColor: "#1E9D66 #f1f1f1",
        }}
      >
        <div className="space-y-6">
          {results.map((property, index) => {
            const imageData = getPropertyImage(property);

            return (
              <div key={property.bagid || index} className="group">
                <div
                  className="flex gap-4 p-4 rounded-lg transition-all duration-200"
                  style={{
                    backgroundColor: "#FBFBFB",
                    border: "1px solid rgba(48, 57, 105, 0.15)",
                  }}
                >
                  {/* Property Image */}
                  <div
                    className="w-28 h-28 flex-shrink-0 rounded-lg overflow-hidden bg-gray-50"
                    style={{
                      border: "1px solid rgba(48, 57, 105, 0.15)",
                    }}
                  >
                    {imageData.loading ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <div
                          className="animate-spin rounded-full h-6 w-6 border-2 border-t-transparent"
                          style={{ borderColor: "#1E9D66" }}
                        ></div>
                      </div>
                    ) : imageData.url ? (
                      <img
                        src={imageData.url}
                        alt={`${property.street} ${property.housenumber}`}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src =
                            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTEyIiBoZWlnaHQ9IjExMiIgdmlld0JveD0iMCAwIDExMiAxMTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMTIiIGhlaWdodD0iMTEyIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01NiA0MEMyNC44IDQwIDI0IDQ2LjQgMjQgNTZWNzJDMjQgODEuNiAyNC44IDg4IDU2IDg4Qzg3LjIgODggODggODEuNiA4OCA3MlY1NkM4OCA0Ni40IDg3LjIgNDAgNTYgNDBaIiBmaWxsPSIjRDFENUQ5Ii8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iNTIiIHI9IjQiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTI0IDcyTDQwIDU2TDU2IDcyTDcyIDU2TDg4IDcyVjcyQzg4IDgxLjYgODcuMiA4OCA1NiA4OEMyNC44IDg4IDI0IDgxLjYgMjQgNzJWNzJaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=";
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <FaBuilding
                          className="text-2xl"
                          style={{ color: "#818589" }}
                        />
                      </div>
                    )}
                  </div>

                  {/* Property Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start mb-3">
                      <div className="min-w-0 flex-1">
                        <h4
                          className="text-lg font-bold flex items-center mb-1"
                          style={{ color: "black", opacity: 0.8 }}
                        >
                          <FaHome
                            className="mr-2 flex-shrink-0"
                            size={16}
                            style={{ color: "#818589" }}
                          />
                          <span className="truncate">
                            {property.street} {property.housenumber}
                            {property.houseaddition || ""}
                          </span>
                        </h4>
                        <div
                          className="flex items-center text-sm mb-2"
                          style={{ color: "#818589" }}
                        >
                          <FaMapMarkerAlt
                            className="mr-1 flex-shrink-0"
                            size={12}
                          />
                          <span>
                            {property.postcode} {property.city}
                          </span>
                          {property.province && (
                            <span className="ml-2">• {property.province}</span>
                          )}
                        </div>
                      </div>

                      {/* Price Badge */}
                      {property.asking_price && (
                        <div className="ml-4 flex-shrink-0">
                          <span
                            className="inline-flex items-center text-sm font-bold px-3 py-1.5 rounded"
                            style={{
                              backgroundColor: "#1E9D66",
                              color: "white",
                            }}
                          >
                            <FaEuroSign className="mr-1" size={12} />
                            {formatPrice(parseInt(property.asking_price))}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Tags and Status */}
                    <div className="flex flex-wrap gap-2 items-center">
                      {property.market_status && (
                        <span
                          className="text-xs font-bold px-2.5 py-1 rounded"
                          style={{
                            backgroundColor: "#1E9D66",
                            color: "white",
                          }}
                        >
                          {property.market_status}
                        </span>
                      )}

                      {property.date_listed && (
                        <span
                          className="text-xs font-medium px-2.5 py-1 rounded flex items-center"
                          style={{
                            backgroundColor: "#FBFBFB",
                            color: "#818589",
                            border: "1px solid rgba(48, 57, 105, 0.15)",
                          }}
                        >
                          <FaCalendarAlt className="mr-1" size={10} />
                          {formatDate(property.date_listed)}
                        </span>
                      )}

                      {property.bagid && (
                        <span
                          className="text-xs font-medium px-2.5 py-1 rounded"
                          style={{
                            backgroundColor: "#FBFBFB",
                            color: "#1E9D66",
                            border: "1px solid rgba(48, 57, 105, 0.15)",
                          }}
                        >
                          BAG: {property.bagid.slice(-6)}
                        </span>
                      )}

                      {!property.asking_price &&
                        !property.market_status &&
                        !property.date_listed && (
                          <span
                            className="text-xs font-medium px-2.5 py-1 rounded"
                            style={{
                              backgroundColor: "#FBFBFB",
                              color: "#818589",
                              border: "1px solid rgba(48, 57, 105, 0.15)",
                            }}
                          >
                            Beperkte informatie beschikbaar
                          </span>
                        )}
                    </div>
                  </div>
                </div>

                {/* Divider - only show if not last item */}
                {index < results.length - 1 && (
                  <div
                    className="my-4"
                    style={{ borderTop: "1px solid rgba(48, 57, 105, 0.15)" }}
                  ></div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Fixed Footer with Action Buttons */}
      <div
        className="mt-6 pt-4 flex-shrink-0"
        style={{ borderTop: "1px solid rgba(48, 57, 105, 0.15)" }}
      >
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          <button
            onClick={modifyResults}
            className="flex items-center justify-center px-4 py-2 rounded-md font-bold text-sm transition-transform duration-300 hover:scale-105 w-full sm:w-auto"
            style={{
              backgroundColor: "white",
              color: "#1E9D66",
              border: "1px solid #1E9D66",
              minWidth: "200px",
            }}
          >
            <FaRedo className="mr-2" size={16} />
            Zoekopdracht wijzigen
          </button>

          <button
            onClick={clearResults}
            className="flex items-center justify-center px-4 py-2 rounded-md font-bold text-sm transition-transform duration-300 hover:scale-105 w-full sm:w-auto"
            style={{
              backgroundColor: "white",
              color: "#1E9D66",
              border: "1px solid #1E9D66",
              minWidth: "200px",
            }}
          >
            <FaPlus className="mr-2" size={16} />
            Nieuwe zoekopdracht
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper function to format price
const formatPrice = (price: number): string => {
  if (price >= 1000000) {
    return `€${(price / 1000000).toFixed(1).replace(".", ",")}M`;
  } else if (price >= 1000) {
    return `€${(price / 1000).toFixed(0)}K`;
  }
  return new Intl.NumberFormat("nl-NL", {
    style: "currency",
    currency: "EUR",
    maximumFractionDigits: 0,
  }).format(price);
};

// Helper function to format date
const formatDate = (dateString: string): string => {
  if (!dateString) return "";
  try {
    return new Date(dateString).toLocaleDateString("nl-NL", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  } catch {
    return dateString;
  }
};

export default SearchResults;
