// Define the Property interface based on your API response
export interface Property {
  id: string;
  address: string;
  // Add other property fields as needed
  [key: string]: any; // For any additional properties that might exist
}

export interface SearchFormValues {
  search: string;
  sort: "relevance" | "date";
  limit: number;
}

export interface SearchResultsProps {
  results: Property[];
  loading: boolean;
  error: string | null;
}

export interface SearchFormProps {
  onSearch: () => void;
  loading: boolean;
}

export interface AutoSearchContainerProps {
  children: React.ReactNode;
  page: number;
}
