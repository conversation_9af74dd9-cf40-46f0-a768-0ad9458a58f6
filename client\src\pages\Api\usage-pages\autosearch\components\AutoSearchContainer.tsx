import React, { ReactNode } from "react";
import ApiFormContainer from "../../components/ApiFormContainer";
import { AVM as AutoSearchImage } from "../../../../../assets/images/api/APIimages";

const progress = ["Zoeko<PERSON>dracht", "Resultaten"];

interface AutoSearchContainerProps {
  children: ReactNode;
  page: number;
}

const AutoSearchContainer: React.FC<AutoSearchContainerProps> = ({
  children,
  page,
}) => {
  return (
    <ApiFormContainer
      page={page}
      title="AutoSearch API"
      subtitle="Zoek en vind vastgoedobjecten in heel Nederland. De AutoSearch API biedt nauwkeurige en actuele zoekresultaten voor woningen en percelen."
      progress={progress}
      resultSelector={(state) => state.autosearch.results}
      link="https://docs.altum.ai/apis/autosearch-api"
      image={AutoSearchImage}
    >
      {children}
    </ApiFormContainer>
  );
};

export default AutoSearchContainer;
