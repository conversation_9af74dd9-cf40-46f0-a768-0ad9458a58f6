import { AppDispatch } from "../store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";

interface SearchPropertiesParams {
  formData: {
    search: string;
    sort?: string;
    limit?: number;
  };
  apiKey: string;
}

export const searchProperties = createAsyncThunk<
  { output: any; query: any },
  SearchPropertiesParams,
  { rejectValue: string }
>(
  "autosearch/searchProperties",
  async ({ formData, apiKey }, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true,
      };

      const response = await axios.post(
        "/api/v1/mopsus/autosearch",
        {
          formData: {
            search: formData.search,
            sort: formData.sort,
            limit: formData.limit,
          },
          apiKey,
        },
        config,
      );

      return {
        output: response.data,
        query: formData,
      };
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || "An error occurred during the search";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  },
);

export const clearSearchResults = () => (dispatch: AppDispatch) => {
  dispatch({ type: "autosearch/clearResults" });
};

export const setSearchError =
  (error: string | null) => (dispatch: AppDispatch) => {
    dispatch({ type: "autosearch/setError", payload: error });
  };
