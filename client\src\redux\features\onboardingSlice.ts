import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  addOnboardingResponse,
  getOnboardingQuestions,
  getOnboardingState,
  getOnboardingStatus,
  getTourCompletionStatus,
  getUserQuestion5Response,
  markTourAsCompleted,
} from "../actions/onboardingActions";
import { OnboardingQuestion } from "../../@types";

interface UserQuestion5ResponsePayload {
  question_id: string;
  user_id: string;
  answer_text: string; // User's response, empty string if not provided
}

interface OnboardingState {
  loading: boolean;
  isOnboadingComplete: boolean;
  onboardingQuestions: OnboardingQuestion[] | null | undefined;
  onboardingSteps: any | null;
  userQuestion5Response: UserQuestion5ResponsePayload | null;
  userQuestion5ResponseLoading: boolean;
  userQuestion5ResponseError: any;
  isTourCompleted: boolean;
  tourCompletionLoading: boolean;
}

const initialState: OnboardingState = {
  onboardingQuestions: null,
  onboardingSteps: null,
  loading: false,
  isOnboadingComplete: false,
  userQuestion5Response: null,
  userQuestion5ResponseLoading: false,
  userQuestion5ResponseError: null,
  isTourCompleted: false,
  tourCompletionLoading: true,
};

const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getOnboardingStatus.fulfilled, (state, action) => {
        state.isOnboadingComplete = action.payload;
      })
      .addCase(getOnboardingState.pending, (state, action) => {
        state.loading = true;
      })
      .addCase(getOnboardingState.fulfilled, (state, action) => {
        state.loading = false;
        state.onboardingSteps = action.payload;
      })
      .addCase(getOnboardingQuestions.fulfilled, (state, action) => {
        state.onboardingQuestions = action.payload;
      })
      .addCase(addOnboardingResponse.pending, (state, action) => {
        state.loading = true;
      })
      .addCase(addOnboardingResponse.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(addOnboardingResponse.rejected, (state, action) => {
        state.loading = false;
      })
      .addCase(getUserQuestion5Response.pending, (state) => {
        state.userQuestion5ResponseLoading = true;
        state.userQuestion5ResponseError = null;
      })
      .addCase(
        getUserQuestion5Response.fulfilled,
        (state, action: PayloadAction<UserQuestion5ResponsePayload>) => {
          state.userQuestion5ResponseLoading = false;
          state.userQuestion5Response = action.payload;
        },
      )
      .addCase(getUserQuestion5Response.rejected, (state, action) => {
        state.userQuestion5ResponseLoading = false;
        state.userQuestion5ResponseError = action.payload;
      })
      .addCase(getTourCompletionStatus.pending, (state) => {
        state.tourCompletionLoading = true;
      })
      .addCase(getTourCompletionStatus.fulfilled, (state, action) => {
        state.tourCompletionLoading = false;
        state.isTourCompleted = action.payload;
      })
      .addCase(getTourCompletionStatus.rejected, (state) => {
        state.tourCompletionLoading = false;
      })
      .addCase(markTourAsCompleted.fulfilled, (state) => {
        state.isTourCompleted = true;
      });
  },
});
export default onboardingSlice.reducer;
