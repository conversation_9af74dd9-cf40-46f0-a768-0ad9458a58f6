import { GoogleGenerativeAI } from "@google/generative-ai";
import { ConfigureOutput, PropertyDetails } from "../@types";
import { generatePrompt, getMaxTokens } from "../utils/propmtUtils";

if (!process.env.GOOGLE_API_KEY) {
  throw new Error("GOOGLE_API_KEY is not defined in the environment variables");
}

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

export async function generatePropertyDescription(
  propertyDetails?: PropertyDetails,
  configureOutput?: ConfigureOutput,
  additionalNotes?: string,
  imageDescriptions?: string[],
) {
  const prompt = generatePrompt(
    propertyDetails,
    configureOutput,
    additionalNotes,
    imageDescriptions,
  );

  try {
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    if (!text) {
      throw new Error("No description generated");
    }
    return text;
  } catch (error) {
    console.error("Error generating property description:", error);
    throw new Error("Failed to generate property description");
  }
}
