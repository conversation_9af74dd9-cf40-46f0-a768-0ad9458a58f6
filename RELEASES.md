# Release Notes

## v2.10.0 - June 19, 2025

### 🚀 New Features

#### Interactive Dashboard Tour (ALTUMAI-2383)

- Introduced an interactive onboarding tour for new users
- Highlights key dashboard features with step-by-step guidance
- Includes navigation controls (Next, Previous, Skip, Exit)
- Accessible via "Take a Tour" button in the help menu
- Fully responsive design for all device sizes
- Tracks tour completion metrics for continuous improvement

### 🐛 Bug Fixes

#### WWS API Usage Page (High Priority)

- **Fixed**: Blank white screen on WWS Points API usage page
- **Root Cause**: Unhandled frontend errors during API response handling
- **Resolution**:
  - Implemented proper error boundaries to prevent UI crashes
  - Added comprehensive error handling for API responses
  - Improved loading states and fallback UI components
  - Added validation for API responses
- **Impact**: Users can now reliably access and use the WWS Points API without experiencing white screens
- **Affected Areas**: WWS Points API usage page, API response handling

#### Subscription Cancellation Feedback (ALTUMAI-2361)

- Enhanced cancellation message to include processing time information
- New message: "Abonnement succesvol opgezegd - verwerking duurt één minuut"
- Better manages user expectations during subscription cancellation

### 🛠️ Improvements

#### Usage page Updates (ALTUMAI-2362)

- Added clear warning for apartment support in Sustainability and NTA 8800 Energylabel APIs
- Warning message: "Let op: het is nog niet mogelijk adviezen te genereren voor appartementen (dit is in ontwikkeling)."
- Updated API documentation and UI elements

#### NTA8800 PDF Document (ALTUMAI-2385)

- Fixed content overlap issues
- Added missing address section
- Redesigned welcome page
- Improved overall document layout and readability
- Ensured brand consistency throughout the document

### 📊 Technical Updates

- Implemented analytics for tracking tour engagement
- Added error boundaries to prevent UI crashes
- Improved accessibility compliance across all new features
- Enhanced error logging and monitoring for API interactions

---

_Note: Some features may be rolled out gradually to all users._
