import React, { useEffect } from "react";
import { useHistory } from "react-router-dom";
import { FormProvider } from "../../components/FormContext";
import AutoSearchContainer from "../components/AutoSearchContainer";
import SearchResults from "../components/SearchResults";
import { useAppSelector } from "../../../../../redux/hooks";
import {
  selectAutosearchResults,
  selectAutosearchLoading,
  selectAutosearchError,
} from "../../../../../redux/features/autosearchSlice";

const AutoSearchResultPage = () => {
  const history = useHistory();
  const results = useAppSelector(selectAutosearchResults);
  const loading = useAppSelector(selectAutosearchLoading);
  const error = useAppSelector(selectAutosearchError);

  // Redirect to form if no results are available
  console.log("results", results);
  useEffect(() => {
    if (!loading && results.length === 0 && !error) {
      history.replace("/autosearch");
    }
  }, [results, loading, error, history]);

  return (
    <FormProvider>
      <AutoSearchContainer page={2}>
        <SearchResults results={results} loading={loading} error={error} />
      </AutoSearchContainer>
    </FormProvider>
  );
};

export default AutoSearchResultPage;
