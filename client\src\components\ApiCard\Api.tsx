import ApiCard from "./ApiCard";
import { APIdata, APIDataItem } from "./ApisData";
import { FC } from "react";
import { useAppSelector } from "../../redux/hooks";
import { compareTwoStrings } from "string-similarity";

// Map API titles to their corresponding AWS API names
const apiNameMapping: { [key: string]: string } = {
  "Verduurzaming advies": "Sustainability",
  "Kadaster transacties": "CreditAPI",
  "Woning referenties opvragen": "Interactive Reference",
  "Woningwaarde API": "avm",
  "Woningwaarde+ API": "AVM+",
  "Vraagprijs API": "listingprice",
  "AutoSearch API": "AutoSearch API",
  "Locatie-gegevens": "Location-Data",
  "Object geometrie opvragen": "Geometry",
  "Woningkenmerken API": "ObjectData",
  "Foto labelen": "Label API",
  "Conditie score voor badkamer en keuken": "Condition-Score",
  Verhuisdata: "move-data",
  "WOZ waarde": "woz",
  "NTA 8800 Energy label API": "Energy Label API",
  "Energielabel inzichten API": "EnergyLabelInsights",
  "Zonnepanelen dakenscan": "Solar API",
  "Energie & klimaat API": "EnergyClimateAPI",
  Voorzieningen: "amenities",
  "Huurreferentie API": "Rental-Reference-API",
  "Autosuggestie API": "autosuggest",
  "Herbouwwaarde API": "Rebuild",
  "WWS Punten API": "WWSPoints",
  "EPC PDF Verwerking": "EPC",
};

const SIMILARITY_THRESHOLD = 0.3;

type Props = {
  activeCategory: string;
  search: string;
  apiPreferenceKeywords?: string[];
};

const Api: FC<Props> = ({ activeCategory, search, apiPreferenceKeywords }) => {
  const allowedApis = useAppSelector((state) => state.apiUsage.allowedApis);

  // Initial filtering based on allowance, active category, and search term
  const initiallyFilteredData = APIdata.filter((data) => {
    const apiName = apiNameMapping[data.title];
    const isAllowed = allowedApis.includes(apiName);
    const matchesCategoryFilter =
      activeCategory === "Alle producten" || data.category === activeCategory;
    const matchesSearchTerm = data.title
      .toLowerCase()
      .includes(search.toLowerCase());

    return isAllowed && matchesCategoryFilter && matchesSearchTerm;
  });
  let finalFilteredData: APIDataItem[];
  if (!apiPreferenceKeywords) {
    finalFilteredData = initiallyFilteredData;
  } else if (apiPreferenceKeywords && apiPreferenceKeywords.length === 0) {
    // Logic for empty/no preference keywords: Show one API from up to 5 different categories
    finalFilteredData = [];
    const selectedCategories = new Set<string>();
    for (const item of initiallyFilteredData) {
      if (selectedCategories.size >= 5) {
        break; // Stop if we have 5 APIs from 5 different categories
      }
      if (!selectedCategories.has(item.category)) {
        finalFilteredData.push(item);
        selectedCategories.add(item.category);
      }
    }
  } else {
    // Logic for when preference keywords are present (similarity search)
    let matchedByPreferences = initiallyFilteredData.filter((data) => {
      const matchesPreferences = apiPreferenceKeywords?.some((keyword) => {
        const keywordLower = keyword.toLowerCase();
        const titleLower = data.title.toLowerCase();
        const categoryLower = data.category ? data.category.toLowerCase() : "";
        const textLower = data.text ? data.text.toLowerCase() : "";

        const titleSimilarity = compareTwoStrings(titleLower, keywordLower);
        const categorySimilarity = categoryLower
          ? compareTwoStrings(categoryLower, keywordLower)
          : 0;
        const textIncludes = textLower.includes(keywordLower);

        return (
          titleSimilarity >= SIMILARITY_THRESHOLD ||
          categorySimilarity >= SIMILARITY_THRESHOLD ||
          textIncludes
        );
      });
      return matchesPreferences;
    });
    finalFilteredData = matchedByPreferences.slice(0, 4); // Limit to 4 results
  }
  return (
    <>
      {finalFilteredData?.map((item, index) => (
        <ApiCard
          key={index}
          title={item.title}
          text={item.text}
          image={item.img}
          link={item.link}
          category={item.category}
          isNew={item.isNew}
          isFirst={index === 0}
        />
      ))}
    </>
  );
};

export default Api;
