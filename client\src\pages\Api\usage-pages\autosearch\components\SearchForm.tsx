import React, { <PERSON> } from "react";
import { toast } from "react-toastify";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { searchProperties } from "../../../../../redux/actions/autoSearchAction";
import ReturnInputField from "../../../../../components/ReturnInputField";
import { useFormContext } from "../../components/FormContext";
import { InputProps } from "../../../../../@types";
import Button from "../../../../../components/Button";

const searchFields: InputProps[] = [
  {
    label: "Zoekterm",
    name: "search",
    type: "text",
    placeholder: "Bijv. Amsterdam, Wibautstraat 1, 1091 GH",
    value: "",
  },
  {
    label: "Sorteren op",
    name: "sort",
    type: "select",
    options: [
      { label: "Relevantie", value: "relevance" },
      { label: "Datum", value: "date" },
    ],
    value: "relevance",
  },
  {
    label: "Aantal resultaten",
    name: "limit",
    type: "select",
    options: [
      { label: "5", value: 5 },
      { label: "10", value: 10 },
      { label: "20", value: 20 },
      { label: "50", value: 50 },
    ],
    value: 10,
  },
];

interface SearchFormProps {
  onSearch: () => void;
  loading: boolean;
}

const SearchForm: FC<SearchFormProps> = ({ onSearch, loading }) => {
  const { formValues, setFormValues } = useFormContext();
  const dispatch = useAppDispatch();
  const apiKey = useAppSelector((state) => state.auth.user?.api_key);

  const handleSubmit = () => {
    if (!formValues.search) return;

    dispatch(
      searchProperties({
        formData: {
          search: formValues.search.trim(),
          sort: formValues.sort || "relevance",
          limit: formValues.limit || 10,
        },
        apiKey: apiKey || "", // You'll need to get this from your app's state or context
      }),
    )
      .then(() => {
        onSearch();
      })
      .catch((error) => {
        console.error("Search failed:", error);
        toast.error(
          error.message || "Er is een fout opgetreden bij het zoeken",
        );
      });
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <h2 className="text-xl font-semibold mb-6">Zoekopdracht</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-4">
        {searchFields.map((field, index) => (
          <div
            key={index}
            className={field.name === "search" ? "md:col-span-3" : ""}
          >
            <ReturnInputField
              label={field.label}
              type={field.type}
              name={field.name}
              placeholder={field.placeholder}
              options={field.options}
              value={formValues[field.name] || field.value}
              onChange={(e: any) => {
                const value =
                  e?.target?.value !== undefined ? e.target.value : e;
                setFormValues({
                  ...formValues,
                  [field.name]: value,
                });
              }}
            />
          </div>
        ))}
      </div>
      <div className="flex justify-end mt-6">
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!formValues.search}
          isProcessing={loading}
          className="bg-primary hover:bg-primary-dark text-white"
        >
          Zoeken
        </Button>
      </div>
    </div>
  );
};

export default SearchForm;
