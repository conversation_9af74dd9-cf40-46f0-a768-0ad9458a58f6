import React from "react";
import { Text, View, Image } from "@react-pdf/renderer";
import styles from "../../../sustainability/pdf/styles";
import altumLogo from "../../../../../../assets/images/Logo-AltumAI.png";

const FooterComponent: React.FC = () => {
  // Get the current year dynamically
  const currentYear = new Date().getFullYear();

  return (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        PRODUCT VAN ALTUM AI | © {currentYear}
      </Text>
      <Image src={altumLogo} style={styles.footerLogo} />
    </View>
  );
};

export default FooterComponent;
