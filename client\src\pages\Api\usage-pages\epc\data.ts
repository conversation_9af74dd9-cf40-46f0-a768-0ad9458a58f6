import { InputProps } from "../../../../@types";

export const EPCFields: InputProps[] = [
  // EPC API only requires PDF file upload, no additional fields needed
];

export const epcEndpoint = {
  title: "EPC API",
  description:
    "Upload een EPC certificaat (NTA 8800 rapport) om gestructureerde data te extraheren voor verduurzamingsadvies",
  method: "POST",
  url: "https://api.altum.ai/epc/pdf",
  headers: [
    {
      name: "x-api-key",
      type: "string",
      required: true,
      description:
        "Unieke API-sleutel van Altum. Maak er een aan via het Mopsus(https://mopsus.altum.ai)",
    },
    {
      name: "Content-Type",
      type: "string",
      required: false,
      description: "multipart/form-data",
    },
  ],
  responses: [
    { status: 200, description: "Succesvolle reactie" },
    { status: 422, description: "Verkeerde invoer" },
    { status: 403, description: "Verboden" },
  ],
};
