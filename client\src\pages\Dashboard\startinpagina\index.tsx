import DashboardContainer from "../components/Container";
import Header from "../components/Header";
import RecommendedAPI from "./components/RecommendedAPI";
import ActivePlanCard from "./components/ActivePlanCard";
import TotalAPICallCard from "./components/TotalAPICallCard";
import APILogs from "./components/APILogs";
import SelectInput from "../../../components/SelectInput";
import { useEffect, useMemo, useState, useRef } from "react";
import Joyride, { CallBackProps, STATUS, Step, EVENTS } from "react-joyride";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { getUsageLogs } from "../../../redux/actions/apiUsage";
import { knowWhen } from "../../../helpers/time";
import {
  getCredits,
  isPPUPlan,
  isTransactionPlan,
} from "../../../helpers/stripeHelper";
import useIsomorphicLayoutEffect from "../../../hooks/useIsomorphicLayoutEffect";
import apiNameConverter from "../../../helpers/apiNameConverter";
import ActivePlanCardSkelecton from "./components/ActivePlanCardSkelecton";
import APILogSkeleton from "./components/APILogSkeleton";
import { Log } from "../../../@types";
import NotificationBanner from "./components/NotificationBanner";
import QuickActions from "./components/QuickActions";
import UsageQuotaBanner from "./components/UsageQuotaBanner";
import WeeklyUsageChart from "./components/WeeklyUsageChart";
import TourStartModal from "./components/TourStartModal"; // Import the new modal
import {
  getUserQuestion5Response,
  getTourCompletionStatus,
  markTourAsCompleted,
} from "../../../redux/actions/onboardingActions";

interface Option {
  value: string;
  label: string;
}

const Dashboard = () => {
  const dispatch = useAppDispatch();
  const { loading: apiUsageLoading } = useAppSelector(
    (state) => state.apiUsage.usageLogs || { loading: false, logs: [] },
  );
  const { user } = useAppSelector((state) => state.auth);
  const { userQuestion5Response, isTourCompleted, tourCompletionLoading } =
    useAppSelector((state) => state.onboarding);
  const [logsFetched, setLogsFetched] = useState(false);
  const [showNotification, setShowNotification] = useState(true);
  const [showQuotaBanner, setShowQuotaBanner] = useState(true);

  // --- Tour State ---
  const [runTour, setRunTour] = useState(false);
  const [tourSteps, setTourSteps] = useState<Step[]>([]);
  const recommendedApiRef = useRef<HTMLDivElement>(null); // Ref for RecommendedAPI container

  // Check if user has already seen or skipped the tour - start closed
  const [isTourModalOpen, setIsTourModalOpen] = useState(false);

  const initialTourSteps: Step[] = useMemo(
    () => [
      {
        target: "#active-plan-card",
        content:
          "Hier ziet u een samenvatting van uw huidige abonnement, inclusief tegoeden en verlengingsdatum.",
        title: "Uw accountoverzicht",
        disableBeacon: true, // Disable beacon to start tour immediately
      },
      {
        target: "#api-usage-insight-chart",
        content:
          "Bekijk hier uw wekelijkse API-gebruikstrends en -inzichten. Het begrijpen van uw gebruik helpt u uw integratie te optimaliseren en uw abonnement effectief te beheren.",
        title: "Gebruikstrends",
        disableBeacon: true, // Disable beacon to proceed automatically
      },
      {
        target: ".tour-recommended-api-wrapper", // Initial fallback, will be updated
        content:
          "Ontdek krachtige API’s die aansluiten bij uw behoeften. Probeer onze populairste opties, zoals de Sustainability API.",
        title: "Aanbevolen API's",
        placement: "left",
        disableBeacon: true, // Good to have for dynamically targeted steps
      },
      {
        target: ".tour-recommended-api-wrapper", // Fallback target, will be updated
        content:
          "Klaar om aan de slag te gaan? Probeer een van onze aanbevolen API's en ontdek hoe Altum AI jouw werk sneller en slimmer maakt.",
        title: "Bekijk Altum AI in actie",
        placement: "left",
        disableBeacon: true,
      },
    ],
    [],
  );
  useEffect(() => {
    if (user?.user_id) {
      dispatch(getUserQuestion5Response(user?.user_id));
      dispatch(getTourCompletionStatus());
    }
  }, [dispatch, user?.user_id]);

  // Update modal visibility based on tour completion status
  useEffect(() => {
    // Only show modal if loading is complete and tour is not completed
    if (!tourCompletionLoading && !isTourCompleted) {
      setIsTourModalOpen(true);
    } else if (!tourCompletionLoading && isTourCompleted) {
      setIsTourModalOpen(false);
    }
  }, [isTourCompleted, tourCompletionLoading]);
  useEffect(() => {
    // Initialize tour steps. This will be called once due to useMemo and initialTourSteps dependency.
    setTourSteps(initialTourSteps);
  }, [initialTourSteps]);

  // Helper function to ensure an element is visible in the viewport
  const ensureElementIsVisible = (element: HTMLElement) => {
    element.scrollIntoView({
      behavior: "smooth",
      block: "center",
      inline: "nearest",
    });
  };

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, type, index, step } = data;
    const finishedStatuses: string[] = [STATUS.FINISHED, STATUS.SKIPPED];

    if (finishedStatuses.includes(status)) {
      setRunTour(false);
      // Reset steps to initial state in case targets were modified, for next tour run.
      setTourSteps(initialTourSteps);

      // Mark tour as completed in the user's account
      dispatch(markTourAsCompleted());

      // Optional: Record which way the tour ended (completed vs skipped)
      if (status === STATUS.FINISHED) {
        console.log("Tour completed successfully");
      } else if (status === STATUS.SKIPPED) {
        console.log("Tour was skipped by user");
      }
      return;
    }

    // Handle step transitions
    if (type === EVENTS.STEP_BEFORE) {
      // For each step, ensure the target element is visible
      if (step.target) {
        try {
          // Handle both string selectors and direct element references
          const targetElement =
            typeof step.target === "string"
              ? step.target.startsWith("http")
                ? null
                : document.querySelector(step.target)
              : step.target;

          if (targetElement) {
            ensureElementIsVisible(targetElement as HTMLElement);
          }
        } catch (error) {
          console.warn("Invalid selector for tour step:", error);
        }
      }

      // Special handling for specific steps
      switch (index) {
        case 1: // Before showing Step 2 (Usage Trends)
          // Prepare Step 3's target
          if (recommendedApiRef.current) {
            setTourSteps((prevSteps) => {
              const newSteps = [...prevSteps];
              if (newSteps.length > 2) {
                newSteps[2] = {
                  ...newSteps[2],
                  target: recommendedApiRef.current as HTMLElement,
                  disableBeacon: true,
                };
              }
              return newSteps;
            });
          }
          break;

        case 2: // Before showing Step 3 (Recommended APIs)
          if (recommendedApiRef.current) {
            // Ensure the section is visible and scrolled to
            ensureElementIsVisible(recommendedApiRef.current);

            // Find the first start button in the recommended APIs
            const firstStartButton = recommendedApiRef.current.querySelector(
              ".first-api-start-button",
            ) as HTMLElement;

            setTourSteps((prevSteps) => {
              const newSteps = [...prevSteps];
              if (newSteps.length > 3) {
                const targetElement =
                  firstStartButton || recommendedApiRef.current;
                if (targetElement) {
                  newSteps[3] = {
                    ...newSteps[3],
                    target: targetElement,
                    content:
                      "Klaar om aan de slag te gaan? Probeer een van onze aanbevolen API's en ontdek hoe Altum AI jouw werk sneller en slimmer maakt.",
                    disableBeacon: true,
                  };
                }
              }
              return newSteps;
            });
          }
          break;

        case 3: // Before showing Step 4 (API Start Button)
          // Ensure the target button is visible
          try {
            // Check if target is a valid CSS selector (not a URL)
            if (
              typeof step.target === "string" &&
              !step.target.startsWith("http")
            ) {
              const button = document.querySelector(step.target);
              if (button) {
                ensureElementIsVisible(button as HTMLElement);
              }
            } else if (typeof step.target !== "string") {
              // If it's an HTMLElement, use it directly
              ensureElementIsVisible(step.target as HTMLElement);
            }
          } catch (error) {
            console.warn("Error finding target for step 4:", error);
          }
          break;
      }
    }
  };

  const startAppTour = () => {
    setTourSteps(initialTourSteps); // Always start with fresh initial steps, targets will be updated
    setRunTour(true);
  };
  // --- End Tour State ---

  const handleCloseTourModal = () => {
    setIsTourModalOpen(false);
    // Mark tour as completed when user dismisses the modal without starting the tour
    dispatch(markTourAsCompleted());
  };

  const handleStartTourFromModal = () => {
    setIsTourModalOpen(false);
    startAppTour(); // This function already sets runTour=true and initializes steps
    // Note: We don't mark the tour as completed here because the user is just starting it
    // It will be marked as completed when they finish or skip via handleJoyrideCallback
  };

  useIsomorphicLayoutEffect(() => {
    // Only fetch logs once when component mounts and not already loading
    if (!logsFetched && !apiUsageLoading) {
      dispatch(getUsageLogs());
      setLogsFetched(true);
    }
    // We don't include logsFetched in deps array to prevent re-runs
  }, [dispatch, apiUsageLoading]);

  const {
    usage: {
      plan,
      totalCredits,
      used,
      transactionUsed,
      remaining,
      usageToday = 0,
      usageYesterday = 0,
      usageThisWeek = 0,
      usageLastWeek = 0,
      loading,
    },
    usageLogs: { loading: logsLoading, logs },
  } = useAppSelector((state) => state.apiUsage);

  const {
    comingInvoice,
    transactionComingInvoice,
    loading: porterLoading,
  } = useAppSelector((state) => state.portal);

  const options: Option[] = useMemo(
    () => [
      {
        value:
          comingInvoice?.product.name ||
          (plan === "Platform - Free Tier" ? "Altum AI - gratis account" : "-"),
        label:
          comingInvoice?.product.name ||
          (plan === "Platform - Free Tier" ? "Altum AI - gratis account" : "-"),
      },
      ...(transactionComingInvoice?.product.name
        ? [
            {
              value: transactionComingInvoice.product.name,
              label: transactionComingInvoice.product.name,
            },
          ]
        : []),
    ],
    [comingInvoice?.product.name, plan, transactionComingInvoice?.product.name],
  );
  const [currentPlan, setCurrentPlan] = useState(options[0]);
  useEffect(() => {
    if (comingInvoice?.product.name) {
      setCurrentPlan({
        value: comingInvoice.product.name,
        label: comingInvoice.product.name,
      });
    }
  }, [comingInvoice?.product.name]);

  useEffect(() => {
    if (plan === "Platform - Free Tier") {
      setCurrentPlan({
        value: "Altum AI - gratis account",
        label: "Altum AI - gratis account",
      });
    }
  }, [plan]);

  const { isPPU, isTransaction } = useMemo(
    () => ({
      isPPU: !!currentPlan && isPPUPlan(currentPlan.value),
      isTransaction: !!currentPlan && isTransactionPlan(currentPlan.value),
    }),
    [currentPlan],
  );

  const formattedLogs = useMemo(
    () =>
      logs?.map((log: Log) => ({
        ...log,
        api_name: apiNameConverter(log.api_name),
        requestTime: knowWhen(log.requestTime),
      })),
    [logs],
  );

  const apiPreferenceKeywords = useMemo(() => {
    const answerText = userQuestion5Response?.answer_text;
    if (typeof answerText === "string") {
      // If answerText is an empty string, return an empty array
      if (answerText === "") {
        return [];
      }
      // Otherwise, split the non-empty string
      return answerText.split(/,\s*/).filter(Boolean);
    }
    // answerText is undefined or null
    return undefined;
  }, [userQuestion5Response?.answer_text]);

  return (
    <>
      <Joyride
        steps={tourSteps}
        run={runTour}
        continuous
        disableOverlayClose
        disableCloseOnEsc
        hideCloseButton={false}
        showSkipButton
        showProgress={false}
        floaterProps={{
          disableAnimation: true,
          styles: {
            floater: {
              filter: "none",
            },
          },
        }}
        callback={handleJoyrideCallback}
        styles={{
          options: {
            zIndex: 10000,
            primaryColor: "#219653",
            backgroundColor: "#ffffff",
            textColor: "#1a1a1a",
            arrowColor: "#219653",
            overlayColor: "rgba(0, 0, 0, 0.5)",
            width: 350,
          },
          tooltip: {
            borderRadius: "8px",
            backgroundColor: "#219653",
            color: "#ffffff",
            fontSize: "16px",
            padding: "24px",
            maxWidth: 350,
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
          },
          tooltipContainer: {
            textAlign: "left" as const,
          },
          tooltipTitle: {
            fontSize: "20px",
            fontWeight: "bold" as const,
            marginBottom: "12px",
            color: "#ffffff",
            lineHeight: 1.3,
          },
          tooltipContent: {
            fontSize: "16px",
            color: "rgba(255, 255, 255, 0.9)",
            marginBottom: "20px",
            lineHeight: 1.5,
          },
          buttonNext: {
            backgroundColor: "#ffffff",
            color: "#219653",
            fontSize: "14px",
            padding: "8px 20px",
            fontWeight: "600",
            borderRadius: "4px",
            outline: "none",
            border: "none",
            cursor: "pointer",
            transition: "all 0.2s ease",
          },
          buttonBack: {
            marginRight: "12px",
            color: "#ffffff",
            fontSize: "14px",
            fontWeight: "500",
            background: "none",
            border: "none",
            cursor: "pointer",
            opacity: 0.9,
          },
          buttonSkip: {
            color: "#ffffff",
            fontSize: "14px",
            fontWeight: "500",
            opacity: 0.9,
          },
          buttonClose: {
            display: "none",
          },
          tooltipFooter: {
            marginTop: "20px",
            paddingTop: "16px",
            borderTop: "1px solid rgba(255, 255, 255, 0.15)",
            alignItems: "center",
          },
          spotlight: {
            borderRadius: 8,
            boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 0 2px #219653",
          },
        }}
        locale={{
          last: "Aan de slag met Altum AI",
          skip: "Rondleiding overslaan",
          next: "Volgende",
          back: "",
        }}
        tooltipComponent={({
          continuous,
          index,
          size,
          step,
          backProps,
          primaryProps,
          skipProps,
          tooltipProps,
        }) => (
          <div
            {...tooltipProps}
            style={{
              backgroundColor: "#219653",
              color: "#ffffff",
              borderRadius: "4px",
              padding: "20px",
              maxWidth: "360px",
              fontSize: "14px",
            }}
          >
            <button
              {...skipProps}
              style={{
                position: "absolute",
                top: "15px",
                right: "15px",
                background: "none",
                border: "none",
                color: "#ffffff",
                fontSize: "24px",
              }}
            >
              ×
            </button>
            <h2
              style={{
                fontSize: "14px",
                fontWeight: "600",
                marginBottom: "10px",
                color: "#ffffff",
              }}
            >
              {step.title}
            </h2>
            <div
              style={{
                color: "#ffffff",
                marginBottom: "20px",
                fontWeight: "400",
              }}
            >
              {step.content}
            </div>
            <div
              style={{
                display: "flex",
                justifyContent:
                  index === size - 1 ? "flex-end" : "space-between",
                alignItems: "center",
                marginTop: "15px",
              }}
            >
              {index !== size - 1 && (
                <button
                  {...skipProps}
                  style={{
                    background: "none",
                    border: "none",
                    color: "#ffffff",
                    fontWeight: "500",
                    fontStyle: "underline",
                  }}
                >
                  Rondleiding overslaan
                </button>
              )}
              <div
                style={{ display: "flex", gap: "8px", alignItems: "center" }}
              >
                <div style={{ color: "#ffffff", fontSize: "16px" }}>
                  {index + 1}/{size}
                </div>
                <button
                  {...primaryProps}
                  style={{
                    backgroundColor: "#ffffff",
                    color: "#219653",
                    padding: "8px 24px",
                    fontWeight: "500",
                    borderRadius: "4px",
                    border: "none",
                  }}
                >
                  {index === size - 1 ? "Aan de slag met Altum AI" : "Volgende"}
                </button>
              </div>
            </div>
          </div>
        )}
      />
      <DashboardContainer pageTitle="Dashboard - Altum AI">
        <TourStartModal
          isOpen={isTourModalOpen}
          onClose={handleCloseTourModal}
          onStartTour={handleStartTourFromModal}
        />
        <div id="dashboard-header">
          <Header
            title="Overzicht"
            subtitle="Hier vind je een overzicht van jouw API activiteiten"
          />
        </div>

        {showNotification && (
          <NotificationBanner onClose={() => setShowNotification(false)} />
        )}

        {showQuotaBanner && (
          <UsageQuotaBanner onClose={() => setShowQuotaBanner(false)} />
        )}

        <div id="plan-selector-container" className="w-48 mb-8 transition-all">
          {options.length > 1 && (
            <SelectInput
              options={options}
              onChange={(e) => {
                const selectedOption = options.find(
                  (opt) => opt.value === e.target.value,
                );
                if (selectedOption) setCurrentPlan(selectedOption);
              }}
            />
          )}
        </div>
        <div className="flex flex-col md:flex-row gap-8 transition-all w-full">
          <div className="flex flex-col gap-8 lg:gap-16 w-full">
            <div className="flex flex-col md:flex-row md:justify-start lg:justify-between gap-8 lg:gap-16 xl:gap-8 2xl:gap-12">
              {porterLoading || loading ? (
                <ActivePlanCardSkelecton />
              ) : (
                <div id="active-plan-card">
                  <ActivePlanCard
                    currentPlan={
                      porterLoading ? "-" : currentPlan?.value || "-"
                    }
                    creditUsed={
                      isPPU
                        ? used || 0
                        : isTransaction
                        ? transactionUsed || 0
                        : remaining || 0
                    }
                    totalCredit={getCredits(currentPlan?.value) || totalCredits}
                    renewalDate={
                      isPPU
                        ? getFirstOfNextMonth()
                        : isTransaction
                        ? transactionComingInvoice?.subscription
                            ?.current_period_end
                          ? new Date(
                              transactionComingInvoice.subscription
                                .current_period_end * 1000,
                            ).toLocaleDateString()
                          : "-"
                        : comingInvoice?.subscription?.current_period_end
                        ? new Date(
                            comingInvoice.subscription.current_period_end *
                              1000,
                          ).toLocaleDateString()
                        : "-"
                    }
                    nextInvoices={String(
                      isPPU && comingInvoice?.comingEvent?.amount_due
                        ? (comingInvoice.comingEvent.amount_due / 100).toFixed(
                            2,
                          )
                        : isTransaction &&
                          transactionComingInvoice?.comingEvent?.amount_due
                        ? (
                            transactionComingInvoice.comingEvent.amount_due /
                            100
                          ).toFixed(2)
                        : "-",
                    )}
                    usagePercentage={
                      isPPU
                        ? 0
                        : isTransaction
                        ? Math.round((transactionUsed || 0) / 100)
                        : Math.round((used || 0) / 100)
                    }
                    isPPU={isPPU}
                  />
                </div>
              )}
              {porterLoading || loading ? (
                <ActivePlanCardSkelecton />
              ) : (
                <div id="total-api-calls-card">
                  <TotalAPICallCard
                    usageToday={usageToday}
                    usageYesterday={usageYesterday}
                    usageThisWeek={usageThisWeek}
                    usageLastWeek={usageLastWeek}
                  />
                </div>
              )}
            </div>

            {/* Usage Insight Chart */}
            <div id="api-usage-insight-chart">
              <WeeklyUsageChart />
            </div>

            {/* Mobile RecommendedAPI (hidden on desktop) */}
            <div
              className="tour-recommended-api-wrapper lg:hidden"
              id="recommended-api-section-mobile"
            >
              <RecommendedAPI
                ref={recommendedApiRef}
                apiPreferenceKeywords={apiPreferenceKeywords}
              />
            </div>
            {logsLoading && logs?.length === 0 ? (
              <APILogSkeleton />
            ) : (
              <div id="api-logs-section">
                <APILogs logs={formattedLogs || []} />
              </div>
            )}
          </div>
          {/* Desktop RecommendedAPI (hidden on mobile) */}
          <div
            className="tour-recommended-api-wrapper hidden lg:flex lg:flex-col lg:gap-8"
            id="recommended-api-section"
          >
            <RecommendedAPI
              ref={recommendedApiRef}
              apiPreferenceKeywords={apiPreferenceKeywords}
            />
            <QuickActions />
          </div>
        </div>
      </DashboardContainer>
    </>
  );
};

export default Dashboard;

function getFirstOfNextMonth(): string {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
  const nextMonthYear = currentMonth === 11 ? currentYear + 1 : currentYear;

  const firstOfNextMonth = new Date(nextMonthYear, nextMonth, 1);
  return firstOfNextMonth.toLocaleDateString("en-CA", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}
