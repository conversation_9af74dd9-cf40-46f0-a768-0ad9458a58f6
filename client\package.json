{"name": "ts_client", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/free-solid-svg-icons": "^5.14.0", "@fortawesome/react-fontawesome": "^0.1.11", "@mui/material": "^5.10.15", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@react-pdf/layout": "3.6.2", "@react-pdf/renderer": "^4.0.0", "@reduxjs/toolkit": "^1.9.3", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.5", "@types/react-dom": "^18.2.3", "aos": "^2.3.4", "axios": "^0.23.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "date-fns": "^2.22.1", "flowbite-react": "^0.7.5", "history": "^5.0.0", "lottie-react": "^2.3.1", "lucide-react": "^0.475.0", "react": "^18.2.0", "react-basicons": "^0.3.4", "react-code-blocks": "^0.0.9-0", "react-dom": "^18.2.0", "react-dropzone": "^12.0.5", "react-ga": "^3.1.2", "react-helmet": "^6.1.0", "react-icons": "^4.12.0", "react-input-slider": "^6.0.0", "react-joyride": "^2.9.3", "react-markdown": "^9.0.1", "react-pdf": "^9.1.1", "react-querybuilder": "^4.5.2", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-select": "^5.4.0", "react-toastify": "^9.1.3", "recharts": "^2.15.3", "remark-gfm": "^4.0.0", "string-similarity": "^4.0.4", "stripe": "^17.6.0", "styled-components": "^5.3.5", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "universal-cookie": "^4.0.3", "web-vitals": "^2.1.4"}, "scripts": {"build": "craco build", "start": "craco start", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint . --fix"}, "resolutions": {"@react-pdf/renderer": "3.1.2", "@react-pdf/layout": "3.6.2"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "devDependencies": {"@types/aos": "^3.0.4", "@types/react-helmet": "^6.1.6", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-scroll": "^1.8.6", "@types/string-similarity": "^4.0.2", "@types/styled-components": "^5.1.26", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@welldone-software/why-did-you-render": "^7.0.1", "assert": "^2.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-loader": "^4.0.2", "file-loader": "^6.2.0", "https-browserify": "^1.0.0", "husky": "^8.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "source-map-explorer": "^2.5.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "typescript": "^4.9.5"}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "proxy": "http://localhost:5000"}