import React from "react";
import { useHistory } from "react-router-dom";
import AutoSearchContainer from "../components/AutoSearchContainer";
import SearchForm from "../components/SearchForm";
import { FormProvider } from "../../components/FormContext";

const AutoSearchFormPage = () => {
  const history = useHistory();

  const handleSearch = () => {
    history.push("/autosearch/result");
  };

  return (
    <FormProvider>
      <AutoSearchContainer page={1}>
        <SearchForm onSearch={handleSearch} loading={false} />
      </AutoSearchContainer>
    </FormProvider>
  );
};

export default AutoSearchFormPage;
