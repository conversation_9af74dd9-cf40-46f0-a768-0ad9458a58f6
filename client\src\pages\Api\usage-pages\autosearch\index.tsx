import React from "react";
import { Switch, Route, useRouteMatch } from "react-router-dom";
import AutoSearchFormPage from "./form";
import AutoSearchResultPage from "./result";

const AutoSearchPage: React.FC = () => {
  const { path } = useRouteMatch();

  return (
    <Switch>
      <Route exact path={`${path}`} component={AutoSearchFormPage} />
      <Route path={`${path}/result`} component={AutoSearchResultPage} />
    </Switch>
  );
};

export default AutoSearchPage;
