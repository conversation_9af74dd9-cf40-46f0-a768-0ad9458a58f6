import { useState } from "react";
import { FormProvider } from "../../components/FormContext";
import EPCContainer from "../components/EPCContainer";
import EPCResult from "./EPCResult";

const EPCResultPage = () => {
  const [page] = useState(2);

  return (
    <FormProvider>
      <EPCContainer page={page}>
        <EPCResult />
      </EPCContainer>
    </FormProvider>
  );
};

export default EPCResultPage;
